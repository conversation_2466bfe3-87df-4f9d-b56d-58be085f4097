# MLISA Data Studio - Development Docker Image
# Includes development tools, debugging capabilities, and volume mount optimizations

######################################################################
# Build frontend assets
######################################################################
FROM node:18-bullseye-slim AS frontend-build

WORKDIR /app/superset-frontend

# Install dependencies
COPY superset-frontend/package*.json ./
RUN npm ci --prefer-offline --no-audit --no-fund --progress=false

# Build frontend assets
COPY superset-frontend .
RUN npm run build

######################################################################
# Development application image
######################################################################
FROM python:3.10-slim-bookworm

WORKDIR /app

# Environment variables for development
ENV LANG=C.UTF-8 \
    LC_ALL=C.UTF-8 \
    FLASK_APP="superset.app:create_app()" \
    SUPERSET_HOME="/app/superset_home" \
    SUPERSET_PORT=8088 \
    PYTHONPATH="/app/mlisa:/app:/app/pythonpath" \
    FLASK_ENV=development \
    FLASK_DEBUG=1

# Create directories and user (using root for development ease)
RUN mkdir -p /app/pythonpath /app/superset_home /app/scripts \
    && useradd --user-group -d ${SUPERSET_HOME} -m --no-log-init --shell /bin/bash superset

# Install system dependencies including development tools
RUN apt-get update -qq && apt-get install -yqq --no-install-recommends \
        build-essential \
        curl \
        default-libmysqlclient-dev \
        libsasl2-dev \
        libsasl2-modules-gssapi-mit \
        libpq-dev \
        libecpg-dev \
        libldap2-dev \
        git \
        vim \
        less \
        htop \
        procps \
        net-tools \
    && pip install --no-cache-dir --upgrade setuptools pip \
    && rm -rf /var/lib/apt/lists/*

# Copy Superset source and dependencies
COPY --chown=superset:superset superset superset
COPY --chown=superset:superset superset-frontend/package.json superset-frontend/
COPY --chown=superset:superset pyproject.toml setup.py README.md ./
COPY --chown=superset:superset requirements/base.txt requirements/

# Install Python dependencies and Superset
RUN pip install --no-cache-dir -r requirements/base.txt \
    && pip install --no-cache-dir -e .

# Install development dependencies
COPY --chown=superset:superset requirements/development.txt requirements/
RUN pip install --no-cache-dir -r requirements/development.txt || echo "No development requirements found"

# Copy frontend assets
COPY --chown=superset:superset --from=frontend-build /app/superset-frontend/dist superset/static/assets

# Copy MLISA customizations
COPY --chown=superset:superset mlisa /app/mlisa
COPY --chown=superset:superset mlisa/requirements.txt /tmp/requirements.txt
COPY --chown=superset:superset mlisa/docker/docker-bootstrap-mlisa.sh /app/scripts/
COPY --chown=superset:superset mlisa/docker/docker-init-mlisa.sh /app/scripts/

# Install MLISA package and dependencies, setup scripts
RUN cd /app/mlisa && pip install --no-cache-dir -e . \
    && if [ -s /tmp/requirements.txt ]; then pip install --no-cache-dir -r /tmp/requirements.txt; fi \
    && rm -f /tmp/requirements.txt \
    && chmod +x /app/scripts/*.sh

# Install additional development tools
RUN pip install --no-cache-dir \
        ipython \
        ipdb \
        pytest \
        pytest-cov \
        black \
        flake8 \
        mypy

# Keep build tools for development (don't remove build-essential)
# Set permissions for development (more permissive)
RUN chmod -R 755 /app

# Development uses root user for easier debugging and file access
USER root

EXPOSE ${SUPERSET_PORT}
